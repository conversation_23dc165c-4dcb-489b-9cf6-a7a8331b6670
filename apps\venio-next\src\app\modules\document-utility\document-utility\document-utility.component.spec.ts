import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { DocumentUtilityComponent } from './document-utility.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { provideMockStore } from '@ngrx/store/testing'
import {
  CaseInfoFacade,
  DocumentsFacade,
  FieldFacade,
  ReviewFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
  ReviewsetFacade,
  ReviewSetStateService,
  BatchModel,
} from '@venio/data-access/review'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { VenioNotificationService } from '@venio/feature/notification'
import { BehaviorSubject, of, Subject } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ConfirmationDialogService } from '../../../services/confirmation-dialog-service'

describe('DocumentUtilityComponent', () => {
  let component: DocumentUtilityComponent
  let fixture: ComponentFixture<DocumentUtilityComponent>
  let queryParamsSubject: BehaviorSubject<any>
  let mockReviewsetFacade: jest.Mocked<ReviewsetFacade>
  let mockReviewSetState: jest.Mocked<ReviewSetStateService>
  let mockConfirmationDialogService: jest.Mocked<ConfirmationDialogService>
  let mockNotificationService: jest.Mocked<VenioNotificationService>
  let mockSearchResultFacade: jest.Mocked<SearchResultFacade>

  beforeEach(async () => {
    queryParamsSubject = new BehaviorSubject({ test: 'value' })

    // Create mocks
    mockReviewsetFacade = {
      markCurrentDocumentAsReviewedAction: new Subject<number>(),
      markAsReviewed$: jest.fn(),
      fetchReviewSetBatchInfo$: jest.fn(),
      checkInReviewBatch$: jest.fn(),
      checkoutBatchReviewSetAction: new Subject<number>(),
    } as any

    mockReviewSetState = {
      reviewSetId: jest.fn().mockReturnValue(123),
      batchId: jest.fn().mockReturnValue(456),
      reviewsetBatchInfo: {
        set: jest.fn(),
      },
      isBatchInfoLoading: {
        set: jest.fn(),
      },
    } as any

    mockConfirmationDialogService = {
      showConfirmationDialog: jest.fn(),
    } as any

    mockNotificationService = {
      showSuccess: jest.fn(),
      showError: jest.fn(),
    } as any

    mockSearchResultFacade = {
      updateSearchResult: jest.fn(),
      resetSearchResults: jest.fn(),
      getSearchResultFileIds: of([]),
      getDocumentExistsInSearchScope$: of(null),
    } as any

    await TestBed.configureTestingModule({
      imports: [
        DocumentUtilityComponent,
        NoopAnimationsModule,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { queryParams: {} },
            queryParams: queryParamsSubject.asObservable(),
          },
        },
        { provide: ReviewsetFacade, useValue: mockReviewsetFacade },
        { provide: ReviewSetStateService, useValue: mockReviewSetState },
        {
          provide: ConfirmationDialogService,
          useValue: mockConfirmationDialogService,
        },
        {
          provide: VenioNotificationService,
          useValue: mockNotificationService,
        },
        { provide: SearchResultFacade, useValue: mockSearchResultFacade },
        SearchFacade,
        FieldFacade,
        StartupsFacade,
        DocumentsFacade,
        NotificationService,
        ReviewFacade,
        CaseInfoFacade,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DocumentUtilityComponent)
    component = fixture.componentInstance

    // Set up component properties needed for the test
    Object.defineProperty(component, 'projectId', {
      value: 789,
      writable: true,
      configurable: true,
    })

    // Don't call detectChanges() to avoid triggering ngOnInit
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('handleMarkAsReviewed', () => {
    describe('when batchInfo.remainingFiles > 0 (else condition)', () => {
      it('should return fileId and update reviewed icon when batch has remaining files', fakeAsync(() => {
        // Arrange
        const testFileId = 12345
        const batchInfo = {
          remainingFiles: 3,
          batchId: 456,
          totalFiles: 10,
          completedFiles: 7,
          reviewSetId: 123,
          name: 'Test Batch',
          batchStatus: 'Active',
          reviewer: 'Test User',
          totalGeneratedHtmlFiles: 10,
        } as BatchModel

        const markAsReviewedResponse: ResponseModel = {
          status: 'Success',
          data: null,
          message: 'Document marked as reviewed',
        }

        const batchInfoResponse: ResponseModel = {
          status: 'Success',
          data: batchInfo,
          message: 'Batch info fetched',
        }

        // Mock the facade methods
        mockReviewsetFacade.markAsReviewed$.mockReturnValue(
          of(markAsReviewedResponse)
        )
        mockReviewsetFacade.fetchReviewSetBatchInfo$.mockReturnValue(
          of(batchInfoResponse)
        )

        // Mock component methods
        jest.spyOn(component, 'isDocumentReviewed').mockReturnValue(false)
        jest.spyOn(component, 'selectedDocumentMetadata').mockReturnValue({
          metadata: [
            { key: '__isReviewed', value: 'No' },
            { key: 'title', value: 'Test Document' },
          ],
        } as any)

        // Act - trigger the action and call the method
        component.handleMarkAsReviewed()
        mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next(testFileId)

        // Advance time to allow observables to complete
        tick()

        // Assert
        // Verify that batch info was fetched
        expect(
          mockReviewsetFacade.fetchReviewSetBatchInfo$
        ).toHaveBeenCalledWith(
          789, // projectId
          123, // reviewSetId
          456 // batchId
        )

        // Verify that batch info was set in state
        expect(mockReviewSetState.reviewsetBatchInfo.set).toHaveBeenCalledWith(
          batchInfo
        )

        // Verify that confirmation dialog was NOT called (since remainingFiles > 0)
        expect(
          mockConfirmationDialogService.showConfirmationDialog
        ).not.toHaveBeenCalled()

        // Verify that batch info loading was set to false
        expect(mockReviewSetState.isBatchInfoLoading.set).toHaveBeenCalledWith(
          false
        )

        // Verify that updateSearchResult was called to update the reviewed icon
        expect(mockSearchResultFacade.updateSearchResult).toHaveBeenCalledWith({
          fileId: testFileId,
          metadata: [
            { key: '__isReviewed', value: 'Yes' },
            { key: 'title', value: 'Test Document' },
          ],
        })

        // Verify success notification was shown
        expect(mockNotificationService.showSuccess).toHaveBeenCalledWith(
          'Document marked as reviewed successfully'
        )
      }))

      it('should handle the case when document is already reviewed', () => {
        // Arrange
        const testFileId = 12345
        jest.spyOn(component, 'isDocumentReviewed').mockReturnValue(true)

        // Act
        mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next(testFileId)
        component.handleMarkAsReviewed()

        // Assert
        // Verify that markAsReviewed$ was not called since document is already reviewed
        expect(mockReviewsetFacade.markAsReviewed$).not.toHaveBeenCalled()
      })

      it('should handle error response from markAsReviewed API', fakeAsync(() => {
        // Arrange
        const testFileId = 12345
        const errorResponse: ResponseModel = {
          status: 'Error',
          data: null,
          message: 'Failed to mark as reviewed',
        }

        mockReviewsetFacade.markAsReviewed$.mockReturnValue(of(errorResponse))
        jest.spyOn(component, 'isDocumentReviewed').mockReturnValue(false)

        // Act
        component.handleMarkAsReviewed()
        mockReviewsetFacade.markCurrentDocumentAsReviewedAction.next(testFileId)

        // Advance time to allow observables to complete
        tick()

        // Assert
        // Verify that fetchReviewSetBatchInfo$ was not called due to error response
        expect(
          mockReviewsetFacade.fetchReviewSetBatchInfo$
        ).not.toHaveBeenCalled()

        // Verify that batch info loading was not set (since the error response doesn't trigger batch info logic)
        expect(mockReviewSetState.isBatchInfoLoading.set).not.toHaveBeenCalled()
      }))
    })
  })
})
