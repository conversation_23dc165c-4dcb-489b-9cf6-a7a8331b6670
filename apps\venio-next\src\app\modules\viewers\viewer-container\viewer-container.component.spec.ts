import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ViewerContainerComponent } from './viewer-container.component'
import { GoldenLayoutContainerInjectionToken } from '@venio/golden-layout'
import { ComponentContainer } from 'golden-layout'
import {
  CaseInfoFacade,
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ViewerContainer', () => {
  let component: ViewerContainerComponent
  let fixture: ComponentFixture<ViewerContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ViewerContainerComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: GoldenLayoutContainerInjectionToken,
          useValue: ComponentContainer,
        },
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        CaseInfoFacade,
        StartupsFacade,
        SearchResultFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ViewerContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('Viewer Tab Visibility', () => {
    beforeEach(() => {
      // Set up default values for testing
      component.currentFileId = 123
      component.hasNativeViewerRight = true
      component.hasFulltextViewerRight = true
      component.hasImageViewerRight = true
      component.hasTranscriptViewerRight = true
      component.isImageTypePdf = false
      component.isTranscriptViewerEnabled = true

      // Mock the hasViewerPanelInLayout method
      jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)
    })

    describe('Native Viewer Tab', () => {
      it('should display Native tab when all conditions are met', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = 123
        jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Native')
        )
        expect(nativeTab).toBeTruthy()
      })

      it('should hide Native tab when hasNativeViewerRight is false', () => {
        component.hasNativeViewerRight = false
        component.currentFileId = 123
        jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Native')
        )
        expect(nativeTab).toBeFalsy()
      })

      it('should hide Native tab when currentFileId is 0', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = 0
        jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Native')
        )
        expect(nativeTab).toBeFalsy()
      })

      it('should hide Native tab when currentFileId is negative', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = -1
        jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Native')
        )
        expect(nativeTab).toBeFalsy()
      })

      it('should hide Native tab when NearNativeViewer panel is not in layout', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = 123
        jest
          .spyOn(component, 'hasViewerPanelInLayout')
          .mockImplementation(
            (panelName: string) =>
              panelName !== component.ReviewPanelType.NearNativeViewer
          )

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Native')
        )
        expect(nativeTab).toBeFalsy()
      })
    })

    describe('Fulltext Viewer Tab', () => {
      it('should display Fulltext tab when all conditions are met', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 123
        jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Fulltext')
        )
        expect(fulltextTab).toBeTruthy()
      })

      it('should hide Fulltext tab when hasFulltextViewerRight is false', () => {
        component.hasFulltextViewerRight = false
        component.currentFileId = 123
        jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Fulltext')
        )
        expect(fulltextTab).toBeFalsy()
      })

      it('should hide Fulltext tab when currentFileId is 0', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 0
        jest.spyOn(component, 'hasViewerPanelInLayout').mockReturnValue(true)

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Fulltext')
        )
        expect(fulltextTab).toBeFalsy()
      })

      it('should hide Fulltext tab when TextViewer panel is not in layout', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 123
        jest
          .spyOn(component, 'hasViewerPanelInLayout')
          .mockImplementation(
            (panelName: string) =>
              panelName !== component.ReviewPanelType.TextViewer
          )

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Fulltext')
        )
        expect(fulltextTab).toBeFalsy()
      })
    })

    describe('PDF Viewer Tab', () => {
      it('should display PDF tab when all conditions are met', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('PDF')
        )
        expect(pdfTab).toBeTruthy()
      })

      it('should hide PDF tab when isImageTypePdf is false', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('PDF')
        )
        expect(pdfTab).toBeFalsy()
      })

      it('should hide PDF tab when hasImageViewerRight is false', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = false
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('PDF')
        )
        expect(pdfTab).toBeFalsy()
      })

      it('should hide PDF tab when currentFileId is 0', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 0
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('PDF')
        )
        expect(pdfTab).toBeFalsy()
      })

      it('should hide PDF tab when ImageViewer panel is not in layout', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.callFake(
          (panelName: string) =>
            panelName !== component.ReviewPanelType.ImageViewer
        )

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('PDF')
        )
        expect(pdfTab).toBeFalsy()
      })
    })

    describe('Tiff Viewer Tab', () => {
      it('should display Tiff tab when all conditions are met', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Tiff')
        )
        expect(tiffTab).toBeTruthy()
      })

      it('should hide Tiff tab when isImageTypePdf is true', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Tiff')
        )
        expect(tiffTab).toBeFalsy()
      })

      it('should hide Tiff tab when hasImageViewerRight is false', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = false
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Tiff')
        )
        expect(tiffTab).toBeFalsy()
      })

      it('should hide Tiff tab when currentFileId is 0', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 0
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.returnValue(true)

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Tiff')
        )
        expect(tiffTab).toBeFalsy()
      })

      it('should hide Tiff tab when ImageViewer panel is not in layout', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123
        ;(component.hasViewerPanelInLayout as jasmine.Spy).and.callFake(
          (panelName: string) =>
            panelName !== component.ReviewPanelType.ImageViewer
        )

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Tiff')
        )
        expect(tiffTab).toBeFalsy()
      })
    })

    describe('Transcript Viewer Tab', () => {
      it('should display Transcript tab when all conditions are met', () => {
        component.isTranscriptViewerEnabled = true
        component.hasTranscriptViewerRight = true

        fixture.detectChanges()

        const transcriptTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Transcript')
        )
        expect(transcriptTab).toBeTruthy()
      })

      it('should hide Transcript tab when isTranscriptViewerEnabled is false', () => {
        component.isTranscriptViewerEnabled = false
        component.hasTranscriptViewerRight = true

        fixture.detectChanges()

        const transcriptTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Transcript')
        )
        expect(transcriptTab).toBeFalsy()
      })

      it('should hide Transcript tab when hasTranscriptViewerRight is false', () => {
        component.isTranscriptViewerEnabled = true
        component.hasTranscriptViewerRight = false

        fixture.detectChanges()

        const transcriptTab = fixture.debugElement.query((el) =>
          el.nativeElement.textContent?.includes('Transcript')
        )
        expect(transcriptTab).toBeFalsy()
      })
    })
  })
})
