import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ViewerContainerComponent } from './viewer-container.component'
import { GoldenLayoutContainerInjectionToken } from '@venio/golden-layout'
import { ComponentContainer } from 'golden-layout'
import {
  CaseInfoFacade,
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResultFacade,
  StartupsFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { WINDOW, windowFactory } from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { By } from '@angular/platform-browser'

describe('ViewerContainer', () => {
  let component: ViewerContainerComponent
  let fixture: ComponentFixture<ViewerContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ViewerContainerComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: GoldenLayoutContainerInjectionToken,
          useValue: ComponentContainer,
        },
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        CaseInfoFacade,
        StartupsFacade,
        SearchResultFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: '1',
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ViewerContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should render the tabstrip component', () => {
    fixture.detectChanges()
    const tabstrip = fixture.debugElement.query(By.css('kendo-tabstrip'))
    expect(tabstrip).toBeTruthy()
  })

  describe('Viewer Tab Visibility', () => {
    let hasViewerPanelInLayoutSpy: jest.SpyInstance

    beforeEach(() => {
      // Set up default values for testing
      component.currentFileId = 123
      component.hasNativeViewerRight = true
      component.hasFulltextViewerRight = true
      component.hasImageViewerRight = true
      component.hasTranscriptViewerRight = true
      component.isImageTypePdf = false
      component.isTranscriptViewerEnabled = true

      // Mock the hasViewerPanelInLayout method to return true for all panel types
      hasViewerPanelInLayoutSpy = jest
        .spyOn(component, 'hasViewerPanelInLayout')
        .mockImplementation((panelName: string) => {
          // Return true for all viewer panel types to allow tabs to be displayed
          return [
            component.ReviewPanelType.NearNativeViewer,
            component.ReviewPanelType.TextViewer,
            component.ReviewPanelType.ImageViewer,
          ].includes(panelName as any)
        })
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    describe('Native Viewer Tab', () => {
      it('should display Native tab when all conditions are met', () => {
        // Set all required conditions for Native tab to be visible
        component.hasNativeViewerRight = true
        component.currentFileId = 123

        // Ensure the mock returns true for NearNativeViewer panel
        hasViewerPanelInLayoutSpy.mockImplementation((panelName: string) => {
          return panelName === component.ReviewPanelType.NearNativeViewer
        })

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Native"]')
        )
        expect(nativeTab).toBeTruthy()
      })

      it('should hide Native tab when hasNativeViewerRight is false', () => {
        component.hasNativeViewerRight = false
        component.currentFileId = 123

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Native"]')
        )
        expect(nativeTab).toBeFalsy()
      })

      it('should hide Native tab when currentFileId is 0', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = 0

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Native"]')
        )
        expect(nativeTab).toBeFalsy()
      })

      it('should hide Native tab when currentFileId is negative', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = -1

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Native"]')
        )
        expect(nativeTab).toBeFalsy()
      })

      it('should hide Native tab when NearNativeViewer panel is not in layout', () => {
        component.hasNativeViewerRight = true
        component.currentFileId = 123
        hasViewerPanelInLayoutSpy.mockImplementation(
          (panelName: string) =>
            panelName !== component.ReviewPanelType.NearNativeViewer
        )

        fixture.detectChanges()

        const nativeTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Native"]')
        )
        expect(nativeTab).toBeFalsy()
      })
    })

    describe('Fulltext Viewer Tab', () => {
      it('should display Fulltext tab when all conditions are met', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 123

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Fulltext"]')
        )
        expect(fulltextTab).toBeTruthy()
      })

      it('should hide Fulltext tab when hasFulltextViewerRight is false', () => {
        component.hasFulltextViewerRight = false
        component.currentFileId = 123

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Fulltext"]')
        )
        expect(fulltextTab).toBeFalsy()
      })

      it('should hide Fulltext tab when currentFileId is 0', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 0

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Fulltext"]')
        )
        expect(fulltextTab).toBeFalsy()
      })

      it('should hide Fulltext tab when TextViewer panel is not in layout', () => {
        component.hasFulltextViewerRight = true
        component.currentFileId = 123
        hasViewerPanelInLayoutSpy.mockImplementation(
          (panelName: string) =>
            panelName !== component.ReviewPanelType.TextViewer
        )

        fixture.detectChanges()

        const fulltextTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Fulltext"]')
        )
        expect(fulltextTab).toBeFalsy()
      })
    })

    describe('PDF Viewer Tab', () => {
      it('should display PDF tab when all conditions are met', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123
        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="PDF"]')
        )
        expect(pdfTab).toBeTruthy()
      })

      it('should hide PDF tab when isImageTypePdf is false', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="PDF"]')
        )
        expect(pdfTab).toBeFalsy()
      })

      it('should hide PDF tab when hasImageViewerRight is false', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = false
        component.currentFileId = 123

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="PDF"]')
        )
        expect(pdfTab).toBeFalsy()
      })

      it('should hide PDF tab when currentFileId is 0', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 0

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="PDF"]')
        )
        expect(pdfTab).toBeFalsy()
      })

      it('should hide PDF tab when ImageViewer panel is not in layout', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123
        hasViewerPanelInLayoutSpy.mockImplementation(
          (panelName: string) =>
            panelName !== component.ReviewPanelType.ImageViewer
        )

        fixture.detectChanges()

        const pdfTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="PDF"]')
        )
        expect(pdfTab).toBeFalsy()
      })
    })

    describe('Tiff Viewer Tab', () => {
      it('should display Tiff tab when all conditions are met', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123
        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Tiff"]')
        )
        expect(tiffTab).toBeTruthy()
      })

      it('should hide Tiff tab when isImageTypePdf is true', () => {
        component.isImageTypePdf = true
        component.hasImageViewerRight = true
        component.currentFileId = 123

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Tiff"]')
        )
        expect(tiffTab).toBeFalsy()
      })

      it('should hide Tiff tab when hasImageViewerRight is false', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = false
        component.currentFileId = 123

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Tiff"]')
        )
        expect(tiffTab).toBeFalsy()
      })

      it('should hide Tiff tab when currentFileId is 0', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 0

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Tiff"]')
        )
        expect(tiffTab).toBeFalsy()
      })

      it('should hide Tiff tab when ImageViewer panel is not in layout', () => {
        component.isImageTypePdf = false
        component.hasImageViewerRight = true
        component.currentFileId = 123
        hasViewerPanelInLayoutSpy.mockImplementation(
          (panelName: string) =>
            panelName !== component.ReviewPanelType.ImageViewer
        )

        fixture.detectChanges()

        const tiffTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Tiff"]')
        )
        expect(tiffTab).toBeFalsy()
      })
    })

    describe('Transcript Viewer Tab', () => {
      it('should display Transcript tab when all conditions are met', () => {
        component.isTranscriptViewerEnabled = true
        component.hasTranscriptViewerRight = true

        fixture.detectChanges()

        const transcriptTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Transcript"]')
        )
        expect(transcriptTab).toBeTruthy()
      })

      it('should hide Transcript tab when isTranscriptViewerEnabled is false', () => {
        component.isTranscriptViewerEnabled = false
        component.hasTranscriptViewerRight = true

        fixture.detectChanges()

        const transcriptTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Transcript"]')
        )
        expect(transcriptTab).toBeFalsy()
      })

      it('should hide Transcript tab when hasTranscriptViewerRight is false', () => {
        component.isTranscriptViewerEnabled = true
        component.hasTranscriptViewerRight = false

        fixture.detectChanges()

        const transcriptTab = fixture.debugElement.query(
          By.css('kendo-tabstrip-tab[title="Transcript"]')
        )
        expect(transcriptTab).toBeFalsy()
      })
    })
  })
})
